

from locust import HttpUser, task, between


# BlogUser 可以把它作为一个真实的用户，其中的 task 是该真实用户在某个场景下的行为模拟
# 比如用户浏览博客这个场景，它有很多行为，比如浏览、留言、发表、登录注册等，这些行为在 locust 中就代表为 task
class BlogUser(HttpUser):
    # 设置目标域名
    host = "https://www.baidu.com"

    # 同一个 user，任务之间的等待时间
    # 有几种设置方式：
    # 1. constant 固定时间，比如2，表示任务执行完成后等待2秒
    # 2. between 随机时间
    # 3. constant_throughput 固定吞吐量，每秒运行多少任务
    # 4. constant_pacing 固定时间，但是会包含任务的执行时间，比如2，任务执行1秒，就会再等待1秒，而不是2秒
    wait_time = between(1,3)


    # 用户刚启动的时候会执行一次
    def on_start(self) -> None:
        pass

    def login(self):
        # self.client 其实就是 requests 库
        self.client.get("/login")

    # 将方法指定为需要执行的性能测试任务，使用 task 装饰器
    # task 装饰器还能指定一个数字，用于表示该行为的权重
    # 如果一个 user 定义了很多 task，权重越大，被执行的概率越大
    # 假设权重设置为 1、2、3，那么每个 task 被执行的次数，也是区域 1:2:3
    @task(10)
    def search(self):
        self.client.get("/search")
        print("用户开始搜索了")
        pass